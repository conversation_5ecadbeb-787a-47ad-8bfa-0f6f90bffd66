#!/bin/bash

echo "🔐 Setting up Android signing for Pixs..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Step 1: Generate keystore
echo -e "${BLUE}📝 Generating keystore...${NC}"
keytool -genkeypair -v -keystore pixs-release-key.keystore -alias pixs-key-alias -keyalg RSA -keysize 2048 -validity 10000 -storetype PKCS12 -dname "CN=Pixs App, OU=Development, O=Your Company, L=Your City, S=Your State, C=Your Country"

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Keystore generated successfully!${NC}"
else
    echo -e "${RED}❌ Failed to generate keystore${NC}"
    exit 1
fi

# Step 2: Move keystore
echo -e "${BLUE}📁 Moving keystore to android/app...${NC}"
mkdir -p android/app
mv pixs-release-key.keystore android/app/

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Keystore moved successfully!${NC}"
else
    echo -e "${RED}❌ Failed to move keystore${NC}"
    exit 1
fi

# Step 3: Create key.properties
echo -e "${BLUE}⚙️ Creating key.properties...${NC}"
cat > android/key.properties << EOF
storePassword=your_keystore_password
keyPassword=your_key_password
keyAlias=pixs-key-alias
storeFile=pixs-release-key.keystore
EOF

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ key.properties created successfully!${NC}"
else
    echo -e "${RED}❌ Failed to create key.properties${NC}"
    exit 1
fi

# Step 4: Update .gitignore
echo -e "${BLUE}🔒 Updating .gitignore...${NC}"
if ! grep -q "android/key.properties" .gitignore 2>/dev/null; then
    echo "" >> .gitignore
    echo "# Android signing" >> .gitignore
    echo "android/key.properties" >> .gitignore
    echo "android/app/pixs-release-key.keystore" >> .gitignore
    echo "*.keystore" >> .gitignore
    echo -e "${GREEN}✅ .gitignore updated!${NC}"
else
    echo -e "${YELLOW}⚠️  .gitignore already contains signing entries${NC}"
fi

echo ""
echo -e "${GREEN}🎉 Signing setup complete!${NC}"
echo ""
echo -e "${YELLOW}⚠️  IMPORTANT NEXT STEPS:${NC}"
echo "   1. ${BLUE}Edit android/key.properties${NC} and replace:"
echo "      - your_keystore_password with your actual keystore password"
echo "      - your_key_password with your actual key password"
echo ""
echo "   2. ${BLUE}Update android/app/build.gradle${NC} with signing configuration"
echo "      (Use the complete build.gradle provided in the guide)"
echo ""
echo "   3. ${BLUE}Keep your keystore file secure!${NC}"
echo "      - Backup the keystore file"
echo "      - Store passwords securely"
echo "      - Don't lose the keystore (you can't update the app without it)"
echo ""
echo -e "${GREEN}🚀 Ready to build:${NC}"
echo "   flutter build appbundle --release"
echo ""

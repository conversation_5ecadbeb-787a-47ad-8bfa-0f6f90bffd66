name: pixs
description: "Discover and download beautiful high-quality photos from Unsplash. Browse curated collections, set wallpapers, and find the perfect image for any project."
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ^3.5.1

dependencies:
  animate_do: ^3.3.3
  async_wallpaper: ^2.1.0
  bloc: ^8.1.4
  cached_network_image: ^3.4.1
  carousel_slider_plus: ^7.0.1
  cupertino_icons: ^1.0.8
  dio: ^5.6.0
  dio_smart_retry: ^6.0.0
  equatable: ^2.0.5
  firebase_core: ^4.0.0
  firebase_remote_config: ^6.0.0
  fluentui_system_icons: ^1.1.255
  flutter:
    sdk: flutter
  flutter_animate: ^4.5.0
  flutter_bloc: ^8.1.6
  flutter_blurhash: ^0.8.2
  flutter_dotenv: ^5.2.1
  flutter_file_dialog: ^3.0.2
  flutter_screenutil: ^5.9.3
  flutter_secure_storage: ^9.2.2
  flutter_staggered_animations: ^1.1.1
  flutter_staggered_grid_view: ^0.7.0
  flutter_svg: ^2.0.10+1
  freezed_annotation: ^2.4.4
  get_it: ^7.7.0
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  http: ^1.2.0
  image_editor_plus: ^1.0.6
  in_app_review: ^2.0.9
  injectable: ^2.4.4
  intl: ^0.19.0
  json_annotation: ^4.9.0
  lottie: ^2.7.0
  path_provider: ^2.1.5
  shimmer: ^3.0.0
  simple_animations: ^5.0.2
  url_launcher: ^6.3.0

dev_dependencies:
  build_runner: ^2.4.12
  flutter_lints: ^4.0.0
  flutter_test:
    sdk: flutter
  freezed: ^2.5.7
  hive_generator: ^2.0.1
  injectable_generator: ^2.6.2
  json_serializable: ^6.8.0

flutter:
  uses-material-design: true

  assets:
    - assets/icons/logo.png
    - assets/
    - assets/icons/
    - assets/animations/
    - .env

fonts:
  - family: Urbanist
    fonts:
      - asset: assets/fonts/urbanist/Urbanist-Bold.ttf
      - asset: assets/fonts/urbanist/Urbanist-ExtraBold.ttf
      - asset: assets/fonts/urbanist/Urbanist-ExtraLight.ttf
      - asset: assets/fonts/urbanist/Urbanist-Light.ttf
      - asset: assets/fonts/urbanist/Urbanist-Medium.ttf
      - asset: assets/fonts/urbanist/Urbanist-Regular.ttf
      - asset: assets/fonts/urbanist/Urbanist-SemiBold.ttf
      - asset: assets/fonts/urbanist/Urbanist-Thin.ttf

# Android Signing Guide for Pixs

## 🔐 **Step 1: Generate Release Keystore**

### **Command to Generate Keystore**
```bash
keytool -genkey -v -keystore pixs-release-key.keystore -alias pixs-key-alias -keyalg RSA -keysize 2048 -validity 10000
```

### **Alternative Command (with more options)**
```bash
keytool -genkeypair -v -keystore pixs-release-key.keystore -alias pixs-key-alias -keyalg RSA -keysize 2048 -validity 10000 -storetype PKCS12 -dname "CN=Pixs App, OU=Development, O=Your Company, L=Your City, S=Your State, C=Your Country"
```

### **Parameters Explained:**
- `-keystore pixs-release-key.keystore`: Name of the keystore file
- `-alias pixs-key-alias`: Alias for the key
- `-keyalg RSA`: Encryption algorithm
- `-keysize 2048`: Key size in bits
- `-validity 10000`: Validity in days (27+ years)
- `-storetype PKCS12`: Keystore format

## 📁 **Step 2: Move Keystore to Android Directory**

### **Command to Move Keystore**
```bash
# Create the directory if it doesn't exist
mkdir -p android/app

# Move the keystore to the android/app directory
mv pixs-release-key.keystore android/app/
```

### **Verify Keystore Location**
```bash
ls -la android/app/pixs-release-key.keystore
```

## ⚙️ **Step 3: Create Key Properties File**

### **Create key.properties file**
```bash
touch android/key.properties
```

### **Add the following content to android/key.properties:**
```properties
storePassword=your_keystore_password
keyPassword=your_key_password
keyAlias=pixs-key-alias
storeFile=pixs-release-key.keystore
```

**⚠️ Important:** Replace `your_keystore_password` and `your_key_password` with the passwords you used when creating the keystore.

## 🔧 **Step 4: Update build.gradle**

### **Add the following to android/app/build.gradle (after the plugins section):**

```gradle
def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

android {
    // ... existing android config ...

    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
        }
    }

    buildTypes {
        release {
            signingConfig signingConfigs.release
            minifyEnabled false
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
        debug {
            minifyEnabled false
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
}
```

## 🚀 **Step 5: Build Commands**

### **Build Release APK**
```bash
flutter build apk --release
```

### **Build Release App Bundle (Recommended for Play Store)**
```bash
flutter build appbundle --release
```

### **Build with Specific Target**
```bash
# For specific architecture
flutter build apk --release --target-platform android-arm64

# For multiple architectures
flutter build apk --release --split-per-abi
```

## 📱 **Step 6: Locate Built Files**

### **APK Location**
```bash
# Single APK
ls -la build/app/outputs/flutter-apk/app-release.apk

# Split APKs
ls -la build/app/outputs/flutter-apk/
```

### **App Bundle Location**
```bash
ls -la build/app/outputs/bundle/release/app-release.aab
```

## 🔍 **Step 7: Verify Signing**

### **Check APK Signature**
```bash
jarsigner -verify -verbose -certs build/app/outputs/flutter-apk/app-release.apk
```

### **Check App Bundle Signature**
```bash
jarsigner -verify -verbose -certs build/app/outputs/bundle/release/app-release.aab
```

## 📋 **Complete Setup Script**

### **Create setup-signing.sh**
```bash
#!/bin/bash

echo "🔐 Setting up Android signing for Pixs..."

# Step 1: Generate keystore
echo "📝 Generating keystore..."
keytool -genkeypair -v -keystore pixs-release-key.keystore -alias pixs-key-alias -keyalg RSA -keysize 2048 -validity 10000 -storetype PKCS12 -dname "CN=Pixs App, OU=Development, O=Your Company, L=Your City, S=Your State, C=Your Country"

# Step 2: Move keystore
echo "📁 Moving keystore to android/app..."
mkdir -p android/app
mv pixs-release-key.keystore android/app/

# Step 3: Create key.properties
echo "⚙️ Creating key.properties..."
cat > android/key.properties << EOF
storePassword=your_keystore_password
keyPassword=your_key_password
keyAlias=pixs-key-alias
storeFile=pixs-release-key.keystore
EOF

echo "✅ Signing setup complete!"
echo "⚠️  Remember to:"
echo "   1. Replace passwords in android/key.properties"
echo "   2. Update android/app/build.gradle with signing config"
echo "   3. Keep your keystore file secure!"
```

### **Make script executable and run**
```bash
chmod +x setup-signing.sh
./setup-signing.sh
```

## 🔄 **Step 8: Update build.gradle (Complete Version)**

### **Replace your android/app/build.gradle with:**

```gradle
plugins {
    id "com.android.application"
    // START: FlutterFire Configuration
    id 'com.google.gms.google-services'
    // END: FlutterFire Configuration
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
}

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

android {
    namespace = "com.appgenie.pixs"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = flutter.ndkVersion

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_1_8
    }

    buildFeatures {
        buildConfig = true
    }

    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
        }
    }

    defaultConfig {
        applicationId = "com.appgenie.pixs"
        minSdk = 23
        targetSdk = flutter.targetSdkVersion
        versionCode = flutter.versionCode
        versionName = flutter.versionName
        multiDexEnabled true
        
        // Add app name for Play Store
        resValue "string", "app_name", "Pixs"
    }

    buildTypes {
        release {
            signingConfig signingConfigs.release
            minifyEnabled false
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
        debug {
            minifyEnabled false
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
}

flutter {
    source = "../.."
}

dependencies {
    implementation 'androidx.multidex:multidex:2.0.1'
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.10"
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.10"
}
```

## 🎯 **Quick Commands Summary**

### **One-liner keystore generation:**
```bash
keytool -genkeypair -v -keystore android/app/pixs-release-key.keystore -alias pixs-key-alias -keyalg RSA -keysize 2048 -validity 10000 -storetype PKCS12 -dname "CN=Pixs App, OU=Development, O=Your Company, L=Your City, S=Your State, C=Your Country"
```

### **Build for Play Store:**
```bash
flutter build appbundle --release
```

### **Build APK for testing:**
```bash
flutter build apk --release
```

## 🔒 **Security Best Practices**

### **Keystore Security:**
- [ ] Store keystore in a secure location
- [ ] Backup keystore file
- [ ] Use strong passwords
- [ ] Don't commit keystore to version control
- [ ] Keep key.properties secure

### **Add to .gitignore:**
```gitignore
# Android signing
android/key.properties
android/app/pixs-release-key.keystore
*.keystore
```

## 📊 **File Structure After Setup**

```
your_project/
├── android/
│   ├── app/
│   │   ├── pixs-release-key.keystore
│   │   └── build.gradle (updated)
│   └── key.properties
├── build/
│   └── app/
│       └── outputs/
│           ├── flutter-apk/
│           │   └── app-release.apk
│           └── bundle/
│               └── release/
│                   └── app-release.aab
└── ...
```

## 🚨 **Troubleshooting**

### **Common Issues:**

1. **"Keystore was tampered with"**
   - Check if keystore file is corrupted
   - Regenerate keystore if needed

2. **"Invalid keystore format"**
   - Ensure using PKCS12 format
   - Check keystore file path

3. **"Password verification failed"**
   - Verify passwords in key.properties
   - Check for extra spaces or characters

4. **"Signing config not found"**
   - Ensure signingConfigs is defined before buildTypes
   - Check key.properties file exists

---

**🎯 Ready to sign! Follow these steps in order and your app will be ready for Play Store submission.**

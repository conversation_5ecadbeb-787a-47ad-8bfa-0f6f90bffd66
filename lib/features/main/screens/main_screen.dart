import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pixs/features/category/screens/category_screen.dart';
import 'package:pixs/features/favourite/screens/favourite_screen.dart';
import 'package:pixs/features/home/<USER>/home/<USER>';
import 'package:pixs/features/home/<USER>/home_screen.dart';
import 'package:pixs/features/image_generation/screens/animated_image_generation_screen.dart';
import 'package:pixs/features/prompt_enhancement/screens/prompt_settings_screen.dart';
import 'package:pixs/features/settings/screens/settings_screen.dart';
import 'package:pixs/shared/app/enums/api_fetch_status.dart';
import 'package:pixs/shared/app/extension/helper.dart';
import 'package:pixs/shared/constants/colors.dart';
import 'package:pixs/shared/themes/font_palette.dart';
import 'package:pixs/shared/utils/route_transitions.dart';
import 'package:pixs/shared/widgets/animations.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  int _selectedIndex = 0;
  late final TextEditingController _promptController;

  final List<Map<String, dynamic>> _navigationItems = [
    {
      'page': const HomeScreen(),
      'icon': FluentIcons.home_20_regular,
      'selectedIcon': FluentIcons.home_20_filled,
      'label': 'Home',
      'appTitle': 'Pixs',
    },
    {
      'page': const CategoryScreen(),
      'icon': FluentIcons.grid_20_regular,
      'selectedIcon': FluentIcons.grid_20_filled,
      'label': 'Category',
      'appTitle': 'Category',
    },
    {
      'page': const FavouriteScreen(),
      'icon': FluentIcons.heart_20_regular,
      'selectedIcon': FluentIcons.heart_20_filled,
      'label': 'Favourite',
      'appTitle': 'Favourite',
    },
    {
      'page': const SettingsScreen(),
      'icon': FluentIcons.settings_20_regular,
      'selectedIcon': FluentIcons.settings_20_filled,
      'label': 'Settings',
      'appTitle': 'Settings',
    },
  ];

  @override
  void initState() {
    super.initState();
    _promptController = TextEditingController(
      text: context.read<HomeCubit>().state.prompt ?? '',
    );
    Helper.afterInit(_initialFunction);
  }

  @override
  void dispose() {
    _promptController.dispose();
    super.dispose();
  }

  Future<void> _initialFunction() async {
    final currentContext = context;
    if (!currentContext.mounted) return;
    context.read<HomeCubit>().initializeFeatureFlags();
    context.read<HomeCubit>().getImages();
    context.read<HomeCubit>().getCollections();
    context.read<HomeCubit>().getWishList();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false, // Prevent back button
      child: Scaffold(
      appBar: _selectedIndex == 1
          ? null
          : AppBar(
              centerTitle: true,
              backgroundColor: kBlack,
              elevation: 0,
              title: _selectedIndex == 0
                  ? SizedBox(
                      width: 75.w,
                      height: 35.h,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            'Pixs',
                            style: FontPalette.urbenist20.copyWith(),
                          ),
                          AnimatedAutoAwesomeIcon(
                            size: 18.sp,
                            color: Colors.purple,
                          ),
                        ],
                      ),
                    )
                  : Text(_navigationItems[_selectedIndex]['appTitle']),
              bottom: _selectedIndex == 0 && context.watch<HomeCubit>().state.isGenerateImageFeatureEnabled 
                  ? PreferredSize(
                      preferredSize: Size.fromHeight(60.h),
                      child: Container(
                        margin: EdgeInsets.symmetric(
                            horizontal: 16.w, vertical: 12.h),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              Colors.purple.withOpacity(0.1),
                              Colors.blue.withOpacity(0.1)
                            ],
                          ),
                          borderRadius: BorderRadius.circular(20.r),
                          border: Border.all(
                            color: Colors.purple.withOpacity(0.3),
                            width: 1,
                          ),
                        ),
                        child: CupertinoTextField(
                          controller: _promptController,
                          onChanged: (value) =>
                              context.read<HomeCubit>().updatePrompt(value),
                          placeholder: context.watch<HomeCubit>().state.isEnhancePromptFeatureEnabled
                              ? 'Describe your dream wallpaper in detail... (tap ✨ to enhance)'
                              : 'Describe your dream wallpaper in detail...',
                          placeholderStyle: TextStyle(
                            color: Colors.grey[400],
                            fontSize: 14.sp,
                            fontStyle: FontStyle.italic,
                          ),
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 14.sp,
                          ),
                          decoration: null,
                          padding: EdgeInsets.symmetric(
                            horizontal: 16.w,
                            vertical: 16.h,
                          ),
                          prefix: context.watch<HomeCubit>().state.isEnhancePromptFeatureEnabled
                              ? Padding(
                                  padding: EdgeInsets.only(left: 12.w),
                                  child: AnimatedAutoAwesomeIcon(
                                    color: Colors.purple,
                                    size: 20.sp,
                                    onTap: () {
                                      final prompt =
                                          context.read<HomeCubit>().state.prompt;
                                      if (prompt?.isNotEmpty ?? false) {
                                        // Navigate to prompt enhancement screen
                                        RouteTransitions.slideAndFade(
                                          context,
                                          PromptSettingsScreen(
                                            originalPrompt: prompt!,
                                            onPromptEnhanced: (enhancedPrompt) {
                                              context.read<HomeCubit>().updatePrompt(enhancedPrompt);
                                              _promptController.text = enhancedPrompt;
                                            },
                                          ),
                                        );
                                      }
                                    },
                                  ),
                                )
                              : null,
                          suffix: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              // Enhance Prompt Button - Only show if feature is enabled
                              if (context.watch<HomeCubit>().state.isEnhancePromptFeatureEnabled)
                                AnimatedOpacity(
                                  opacity: context
                                              .watch<HomeCubit>()
                                              .state
                                              .prompt
                                              ?.isNotEmpty ??
                                          false
                                      ? 1.0
                                      : 0.0,
                                  duration: const Duration(milliseconds: 200),
                                  child: AnimatedButton(
                                    color: Colors.blue,
                                    borderRadius: 12,
                                    padding: EdgeInsets.all(8.w),
                                    onTap: () {
                                      final prompt =
                                          context.read<HomeCubit>().state.prompt;
                                      if (prompt?.isNotEmpty ?? false) {
                                         // Navigate to prompt enhancement screen
                                        RouteTransitions.slideAndFade(
                                          context,
                                          PromptSettingsScreen(
                                            originalPrompt: prompt!,
                                            onPromptEnhanced: (enhancedPrompt) {
                                              context.read<HomeCubit>().updatePrompt(enhancedPrompt);
                                              _promptController.text = enhancedPrompt;
                                            },
                                          ),
                                        );
                                      }
                                    },
                                    child: AnimatedAutoAwesomeIcon(
                                      color: Colors.white,
                                      size: 20.sp,
                                    ),
                                  ),
                                ),
                              if (context.watch<HomeCubit>().state.isEnhancePromptFeatureEnabled &&
                                  context.watch<HomeCubit>().state.isGenerateImageFeatureEnabled)
                                SizedBox(width: 8.w),
                              // Generate Image Button - Only show if feature is enabled
                              if (context.watch<HomeCubit>().state.isGenerateImageFeatureEnabled)
                                AnimatedOpacity(
                                  opacity: context
                                              .watch<HomeCubit>()
                                              .state
                                              .prompt
                                              ?.isNotEmpty ??
                                          false
                                      ? 1.0
                                      : 0.0,
                                  duration: const Duration(milliseconds: 200),
                                  child: AnimatedButton(
                                    color: Colors.purple,
                                    borderRadius: 12,
                                    padding: EdgeInsets.all(8.w),
                                    onTap: () {
                                      final prompt =
                                          context.read<HomeCubit>().state.prompt;
                                      if (prompt?.isNotEmpty ?? false) {
                                        // Navigate to the animated image generation screen
                                        RouteTransitions.slideAndFade(
                                          context,
                                          AnimatedImageGenerationScreen(
                                              prompt: prompt!),
                                        );
                                      }
                                    },
                                    child: context
                                                .watch<HomeCubit>()
                                                .state
                                                .imageGenerationStatus ==
                                            ApiFetchStatus.loading
                                        ? SizedBox(
                                            height: 20.sp,
                                            width: 20.sp,
                                            child:
                                                const CircularProgressIndicator(
                                              strokeWidth: 2.0,
                                              color: Colors.white,
                                            ),
                                          )
                                        : Icon(
                                            Icons.bolt,
                                            color: Colors.white,
                                            size: 20.sp,
                                          ),
                                  ),
                                ),
                              SizedBox(width: 12.w),
                            ],
                          ),
                        ),
                      ),
                    )
                  : null,
            ),
      body: Scaffold(
        body: IndexedStack(
          index: _selectedIndex,
          children:
              _navigationItems.map((item) => item['page'] as Widget).toList(),
        ),
        extendBody: true,
        floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
        floatingActionButton: Padding(
          padding: EdgeInsets.only(left: 16.w, right: 16.w),
          child: Container(
            width: double.infinity,
            height: 60.h,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.all(
                Radius.circular(60.r),
              ),
              boxShadow: const [
                BoxShadow(
                  color: kBlack,
                  blurRadius: 20,
                  spreadRadius: 0,
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.all(
                Radius.circular(60.r),
              ),
              child: Theme(
                data: Theme.of(context).copyWith(
                  canvasColor: kPrimaryColor,
                ),
                child: BottomNavigationBar(
                  type: BottomNavigationBarType.fixed,
                  showSelectedLabels: false,
                  showUnselectedLabels: false,
                  elevation: 1,
                  currentIndex: _selectedIndex,
                  onTap: (index) {
                    setState(() {
                      _selectedIndex = index;
                    });
                  },
                  items: _navigationItems.map((item) {
                    return BottomNavigationBarItem(
                      icon: Icon(
                        item['icon'],
                        color: kWhite.withOpacity(0.4),
                      ),
                      activeIcon: Icon(
                        item['selectedIcon'],
                      ),
                      label: '',
                    );
                  }).toList(),
                ),
              ),
            ),
          ),
        ),
      ),
    ), // Scaffold
    ); // PopScope
  }
}

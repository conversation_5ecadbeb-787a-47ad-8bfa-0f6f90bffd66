// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:get_it/get_it.dart' as _i174;
import 'package:injectable/injectable.dart' as _i526;

import '../../features/home/<USER>/repository/home_repository.dart' as _i541;
import '../../features/home/<USER>/services/home_service.dart' as _i677;
import '../../features/home/<USER>/home/<USER>' as _i906;
import '../service/download_tracking_service.dart' as _i48;
import '../service/firebase_remote_config_service.dart' as _i589;

extension GetItInjectableX on _i174.GetIt {
// initializes the registration of main-scope dependencies inside of GetIt
  _i174.GetIt init({
    String? environment,
    _i526.EnvironmentFilter? environmentFilter,
  }) {
    final gh = _i526.GetItHelper(
      this,
      environment,
      environmentFilter,
    );
    gh.lazySingleton<_i48.DownloadTrackingService>(
        () => _i48.DownloadTrackingService());
    gh.lazySingleton<_i589.FirebaseRemoteConfigService>(
        () => _i589.FirebaseRemoteConfigService());
    gh.lazySingleton<_i541.HomeRepository>(() => _i677.HomeService());
    gh.factory<_i906.HomeCubit>(() => _i906.HomeCubit(
          gh<_i541.HomeRepository>(),
          gh<_i589.FirebaseRemoteConfigService>(),
        ));
    return this;
  }
}

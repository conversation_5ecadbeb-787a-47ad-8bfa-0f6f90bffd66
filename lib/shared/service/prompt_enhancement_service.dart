import 'dart:math';

/// A service to enhance user prompts for better AI image generation results
class PromptEnhancementService {
  static final PromptEnhancementService _instance = PromptEnhancementService._internal();

  factory PromptEnhancementService() => _instance;

  PromptEnhancementService._internal();

  /// List of high-quality prompt templates for different styles
  final List<String> _styleTemplates = [
    // Photorealistic templates
    "ultra detailed 8k photography, professional lighting, dramatic composition, {prompt}",
    "hyperrealistic photograph, cinematic lighting, high detail, 8k resolution, {prompt}",
    "photorealistic, detailed texture, professional photography, natural lighting, {prompt}",
    
    // Artistic templates
    "digital art, highly detailed, intricate, {prompt}, vibrant colors, masterpiece",
    "fantasy art style, detailed, vibrant, dramatic lighting, {prompt}, trending on artstation",
    "concept art, detailed, professional, {prompt}, trending on artstation, 8k resolution",
    
    // Specific art styles
    "oil painting style, detailed brushwork, vibrant colors, {prompt}, masterpiece",
    "watercolor painting, soft edges, color bleeding, {prompt}, artistic",
    "anime style, detailed, vibrant colors, {prompt}, studio ghibli inspired",
    "3D render, octane render, {prompt}, highly detailed, professional lighting",
    
    // Landscape specific
    "breathtaking landscape, {prompt}, golden hour lighting, 8k resolution, wide angle",
    "epic scenery, {prompt}, dramatic lighting, atmospheric, detailed, 8k resolution",
    
    // Portrait specific
    "portrait of {prompt}, detailed features, professional studio lighting, 8k resolution",
    "character portrait, {prompt}, detailed features, dramatic lighting, 8k resolution",
  ];

  /// List of quality enhancers to append to prompts
  final List<String> _qualityEnhancers = [
    "highly detailed",
    "8k resolution",
    "professional lighting",
    "masterpiece",
    "intricate details",
    "photorealistic",
    "hyperrealistic",
    "ultra HD",
    "sharp focus",
    "dramatic lighting",
    "vibrant colors",
    "stunning composition",
  ];

  /// List of negative prompts to avoid common issues
  final List<String> _negativePrompts = [
    "blurry",
    "low quality",
    "pixelated",
    "distorted",
    "deformed",
    "disfigured",
    "poorly drawn",
    "bad anatomy",
    "out of frame",
    "cropped",
    "watermark",
    "signature",
    "text",
    "low contrast",
    "oversaturated",
  ];

  /// Enhances a user prompt with additional instructions for better results
  /// 
  /// [userPrompt] - The original prompt from the user
  /// [useTemplate] - Whether to use a style template (true) or just add enhancers (false)
  /// [addNegativePrompt] - Whether to add negative prompts to avoid common issues
  /// [style] - Optional specific style to use (if null, a random style is chosen)
  String enhancePrompt(
    String userPrompt, {
    bool useTemplate = true,
    bool addNegativePrompt = true,
    PromptStyle? style,
  }) {
    if (userPrompt.isEmpty) return userPrompt;
    
    String enhancedPrompt = userPrompt;
    
    // Apply a template if requested
    if (useTemplate) {
      // Select templates based on style or use all templates
      List<String> availableTemplates = _styleTemplates;

      if (style != null) {
        final styleTemplates = _getTemplatesForStyle(style);
        // If no templates found for the specific style, fall back to all templates
        availableTemplates = styleTemplates.isNotEmpty ? styleTemplates : _styleTemplates;
      }

      // Ensure we have templates available
      if (availableTemplates.isNotEmpty) {
        // Choose a random template from the available ones
        final template = availableTemplates[Random().nextInt(availableTemplates.length)];

        // Replace the {prompt} placeholder with the user's prompt
        enhancedPrompt = template.replaceAll('{prompt}', userPrompt);
      } else {
        // Fallback: just add enhancers if no templates available
        final enhancers = _getRandomEnhancers(3);
        enhancedPrompt = '$userPrompt, ${enhancers.join(", ")}';
      }
    } else {
      // Just add some quality enhancers
      final enhancers = _getRandomEnhancers(3); // Get 3 random enhancers
      enhancedPrompt = '$userPrompt, ${enhancers.join(", ")}';
    }
    
    // Add negative prompts if requested
    if (addNegativePrompt) {
      final negatives = _getRandomNegativePrompts(5); // Get 5 random negative prompts
      enhancedPrompt += ' | Negative prompt: ${negatives.join(", ")}';
    }
    
    return enhancedPrompt;
  }
  
  /// Gets a list of templates appropriate for the specified style
  List<String> _getTemplatesForStyle(PromptStyle style) {
    switch (style) {
      case PromptStyle.photorealistic:
        return _styleTemplates.where((t) => 
          t.contains('photography') || 
          t.contains('photorealistic') || 
          t.contains('hyperrealistic')).toList();
      
      case PromptStyle.digitalArt:
        return _styleTemplates.where((t) => 
          t.contains('digital art') || 
          t.contains('concept art') || 
          t.contains('fantasy art')).toList();
      
      case PromptStyle.painting:
        return _styleTemplates.where((t) => 
          t.contains('painting') || 
          t.contains('oil') || 
          t.contains('watercolor')).toList();
      
      case PromptStyle.anime:
        return _styleTemplates.where((t) => 
          t.contains('anime') || 
          t.contains('ghibli')).toList();
      
      case PromptStyle.threeD:
        return _styleTemplates.where((t) => 
          t.contains('3D') || 
          t.contains('render')).toList();
      
      case PromptStyle.landscape:
        return _styleTemplates.where((t) => 
          t.contains('landscape') || 
          t.contains('scenery')).toList();
      
      case PromptStyle.portrait:
        return _styleTemplates.where((t) => 
          t.contains('portrait')).toList();
    }
  }
  
  /// Gets a random selection of quality enhancers
  List<String> _getRandomEnhancers(int count) {
    final enhancers = List<String>.from(_qualityEnhancers);
    enhancers.shuffle();
    return enhancers.take(min(count, enhancers.length)).toList();
  }
  
  /// Gets a random selection of negative prompts
  List<String> _getRandomNegativePrompts(int count) {
    final negatives = List<String>.from(_negativePrompts);
    negatives.shuffle();
    return negatives.take(min(count, negatives.length)).toList();
  }
}

/// Enum representing different prompt styles
enum PromptStyle {
  photorealistic,
  digitalArt,
  painting,
  anime,
  threeD,
  landscape,
  portrait,
}

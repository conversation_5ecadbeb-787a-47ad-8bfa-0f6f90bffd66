import 'dart:developer';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:injectable/injectable.dart';

@lazySingleton
class FirebaseRemoteConfigService {
  static FirebaseRemoteConfig? _remoteConfig;
  static bool _initializationFailed = false;

  // Feature flag keys
  static const String _enhancePromptFeatureKey = 'enable_enhance_prompt_feature';
  static const String _generateImageFeatureKey = 'enable_generate_image_feature';

  // Local fallback values when Firebase Remote Config fails
  static Map<String, bool> _fallbackValues = {
    _enhancePromptFeatureKey: true,
    _generateImageFeatureKey: true,
  };

  /// Initialize Firebase Remote Config
  Future<void> initialize() async {
    try {
      log('Starting Firebase Remote Config initialization...');

      // Try to get the instance first
      _remoteConfig = FirebaseRemoteConfig.instance;
      log('Firebase Remote Config instance obtained');

      // Try to set configuration settings with more conservative values
      try {
        await _remoteConfig!.setConfigSettings(
          RemoteConfigSettings(
            fetchTimeout: const Duration(seconds: 60),
            minimumFetchInterval: const Duration(seconds: 0), // Allow immediate fetching for testing
          ),
        );
        log('Firebase Remote Config settings applied');
      } catch (settingsError) {
        log('Error setting config settings: $settingsError');
        // Continue without custom settings
      }

      // Try to set default values
      try {
        await _remoteConfig!.setDefaults(<String, dynamic>{
          _enhancePromptFeatureKey: true,
          _generateImageFeatureKey: true,
        });
        log('Firebase Remote Config defaults set');
      } catch (defaultsError) {
        log('Error setting defaults: $defaultsError');
        // Continue without defaults
      }

      // Try to fetch and activate remote config
      try {
        await _fetchAndActivate();
        log('Firebase Remote Config fetch and activate completed');
      } catch (fetchError) {
        log('Error during fetch and activate: $fetchError');
        // Continue with local defaults
      }

      log('Firebase Remote Config initialization completed');
    } catch (e, stackTrace) {
      log('Critical error initializing Firebase Remote Config: $e');
      log('Stack trace: $stackTrace');
      // Set _remoteConfig to null to indicate failure
      _remoteConfig = null;
      _initializationFailed = true;
    }
  }

  /// Fetch and activate remote config values
  Future<bool> _fetchAndActivate() async {
    try {
      // First try to fetch
      await _remoteConfig!.fetch();
      log('Remote Config fetch completed');

      // Then activate
      final bool activated = await _remoteConfig!.activate();
      log('Remote Config activate: $activated');

      return activated;
    } catch (e, stackTrace) {
      log('Error fetching remote config: $e');
      log('Stack trace: $stackTrace');
      return false;
    }
  }

  /// Force fetch remote config (useful for testing)
  Future<bool> forceFetch() async {
    try {
      await _remoteConfig!.fetch();
      await _remoteConfig!.activate();
      log('Remote Config force fetched successfully');
      return true;
    } catch (e) {
      log('Error force fetching remote config: $e');
      return false;
    }
  }

  /// Check if enhance prompt feature is enabled
  bool get isEnhancePromptFeatureEnabled {
    // If initialization failed, use fallback values
    if (_initializationFailed || _remoteConfig == null) {
      log('Using fallback value for $_enhancePromptFeatureKey: ${_fallbackValues[_enhancePromptFeatureKey]}');
      return _fallbackValues[_enhancePromptFeatureKey] ?? true;
    }

    try {
      final value = _remoteConfig!.getValue(_enhancePromptFeatureKey);
      log('Raw value for $_enhancePromptFeatureKey: ${value.asString()}');

      // Try to parse as boolean
      final stringValue = value.asString().toLowerCase();
      if (stringValue == 'true') return true;
      if (stringValue == 'false') return false;

      // Fallback to getBool if string parsing fails
      return _remoteConfig!.getBool(_enhancePromptFeatureKey);
    } catch (e, stackTrace) {
      log('Error getting enhance prompt feature flag: $e');
      log('Stack trace: $stackTrace');
      return _fallbackValues[_enhancePromptFeatureKey] ?? true;
    }
  }

  /// Check if generate image feature is enabled
  bool get isGenerateImageFeatureEnabled {
    // If initialization failed, use fallback values
    if (_initializationFailed || _remoteConfig == null) {
      log('Using fallback value for $_generateImageFeatureKey: ${_fallbackValues[_generateImageFeatureKey]}');
      return _fallbackValues[_generateImageFeatureKey] ?? true;
    }

    try {
      final value = _remoteConfig!.getValue(_generateImageFeatureKey);
      log('Raw value for $_generateImageFeatureKey: ${value.asString()}');

      // Try to parse as boolean
      final stringValue = value.asString().toLowerCase();
      if (stringValue == 'true') return true;
      if (stringValue == 'false') return false;

      // Fallback to getBool if string parsing fails
      return _remoteConfig!.getBool(_generateImageFeatureKey);
    } catch (e, stackTrace) {
      log('Error getting generate image feature flag: $e');
      log('Stack trace: $stackTrace');
      return _fallbackValues[_generateImageFeatureKey] ?? true;
    }
  }



  /// Get all feature flags as a map
  Map<String, bool> get allFeatureFlags {
    return {
      'enhancePromptFeature': isEnhancePromptFeatureEnabled,
      'generateImageFeature': isGenerateImageFeatureEnabled,
    };
  }

  /// Get a custom string value from remote config
  String getString(String key, {String defaultValue = ''}) {
    try {
      return _remoteConfig?.getString(key) ?? defaultValue;
    } catch (e) {
      log('Error getting string value for key $key: $e');
      return defaultValue;
    }
  }

  /// Get a custom boolean value from remote config
  bool getBool(String key, {bool defaultValue = false}) {
    try {
      return _remoteConfig?.getBool(key) ?? defaultValue;
    } catch (e) {
      log('Error getting boolean value for key $key: $e');
      return defaultValue;
    }
  }

  /// Get a custom integer value from remote config
  int getInt(String key, {int defaultValue = 0}) {
    try {
      return _remoteConfig?.getInt(key) ?? defaultValue;
    } catch (e) {
      log('Error getting integer value for key $key: $e');
      return defaultValue;
    }
  }

  /// Get a custom double value from remote config
  double getDouble(String key, {double defaultValue = 0.0}) {
    try {
      return _remoteConfig?.getDouble(key) ?? defaultValue;
    } catch (e) {
      log('Error getting double value for key $key: $e');
      return defaultValue;
    }
  }

  /// Listen to remote config updates
  Stream<RemoteConfigUpdate> get onConfigUpdated {
    return _remoteConfig?.onConfigUpdated ?? const Stream.empty();
  }

  /// Get the last fetch time
  DateTime? get lastFetchTime {
    return _remoteConfig?.lastFetchTime;
  }

  /// Get the last fetch status
  RemoteConfigFetchStatus get lastFetchStatus {
    return _remoteConfig?.lastFetchStatus ?? RemoteConfigFetchStatus.noFetchYet;
  }

  /// Check if remote config is available
  bool get isAvailable {
    return _remoteConfig != null;
  }

  /// Get debug information about Remote Config
  Map<String, dynamic> get debugInfo {
    Map<String, dynamic> info = {
      'initializationFailed': _initializationFailed,
      'remoteConfigNull': _remoteConfig == null,
      'usingFallbackValues': _initializationFailed || _remoteConfig == null,
    };

    if (_remoteConfig == null || _initializationFailed) {
      info.addAll({
        'fallbackEnhancePrompt': _fallbackValues[_enhancePromptFeatureKey],
        'fallbackGenerateImage': _fallbackValues[_generateImageFeatureKey],
        'error': 'Remote Config initialization failed - using fallback values'
      });
      return info;
    }

    try {
      info.addAll({
        'initialized': true,
        'lastFetchTime': _remoteConfig!.lastFetchTime.toIso8601String(),
        'lastFetchStatus': _remoteConfig!.lastFetchStatus.toString(),
        'enhancePromptValue': _remoteConfig!.getValue(_enhancePromptFeatureKey).asString(),
        'generateImageValue': _remoteConfig!.getValue(_generateImageFeatureKey).asString(),
        'allKeys': _remoteConfig!.getAll().keys.toList(),
      });
    } catch (e) {
      info.addAll({
        'initialized': true,
        'error': e.toString()
      });
    }

    return info;
  }

  /// Manually set feature flag values (for testing when Remote Config fails)
  void setFallbackValue(String key, bool value) {
    _fallbackValues[key] = value;
    log('Manually set fallback value: $key = $value');
  }

  /// Toggle enhance prompt feature (for testing)
  void toggleEnhancePromptFeature() {
    final currentValue = _fallbackValues[_enhancePromptFeatureKey] ?? true;
    setFallbackValue(_enhancePromptFeatureKey, !currentValue);
  }

  /// Toggle generate image feature (for testing)
  void toggleGenerateImageFeature() {
    final currentValue = _fallbackValues[_generateImageFeatureKey] ?? true;
    setFallbackValue(_generateImageFeatureKey, !currentValue);
  }
}

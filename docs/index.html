<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pixs - Legal Documents</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
        }
        h1 {
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        .subtitle {
            color: #7f8c8d;
            font-size: 1.2em;
            margin-bottom: 40px;
        }
        .card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 30px;
            margin: 20px 0;
            border-left: 4px solid #3498db;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .card h2 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        .card p {
            color: #7f8c8d;
            margin-bottom: 20px;
        }
        .btn {
            display: inline-block;
            background: #3498db;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            transition: background 0.3s ease;
        }
        .btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
        .btn-secondary {
            background: #e74c3c;
        }
        .btn-secondary:hover {
            background: #c0392b;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ecf0f1;
            color: #7f8c8d;
        }
        .app-info {
            background: #e8f5e8;
            padding: 20px;
            border-radius: 12px;
            margin: 30px 0;
        }
        .app-info h3 {
            color: #27ae60;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📱 Pixs</h1>
        <p class="subtitle">Legal Documents & Policies</p>
        
        <div class="app-info">
            <h3>About Pixs</h3>
            <p>Pixs is a beautiful photo discovery app that lets you explore and download high-quality images from Unsplash. Browse curated collections, set wallpapers, and find the perfect image for any project.</p>
        </div>

        <div class="card">
            <h2>🔒 Privacy Policy</h2>
            <p>Learn how we collect, use, and protect your information when you use Pixs. We're committed to transparency and protecting your privacy.</p>
            <a href="privacy-policy.html" class="btn">Read Privacy Policy</a>
        </div>

        <div class="card">
            <h2>📋 Terms of Service</h2>
            <p>Understand the terms and conditions for using Pixs, including image usage rights, user responsibilities, and app policies.</p>
            <a href="terms-of-service.html" class="btn btn-secondary">Read Terms of Service</a>
        </div>

        <div class="footer">
            <p><strong>Pixs</strong> - Discover Beautiful Photography</p>
            <p>© 2024 Pixs. All rights reserved.</p>
            <p>For support: <a href="mailto:<EMAIL>"><EMAIL></a></p>
        </div>
    </div>
</body>
</html>

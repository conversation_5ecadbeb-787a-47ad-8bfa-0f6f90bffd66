# AI Attribution Changes

## Summary
Modified the attribution widget system to hide Unsplash attribution for AI-generated images, since AI-generated images don't come from Unsplash.

## Changes Made

### 1. Modified `AttributionWidget` class
- Added `_isAiGenerated` getter to detect AI-generated images
- Modified `build()` method to return `SizedBox.shrink()` for AI-generated images
- Detection logic: checks if `user.name == 'AI Generated'` or `user.username == 'ai_generated'`

### 2. Modified `CompactAttributionWidget` class
- Added early return with `SizedBox.shrink()` for AI-generated images
- Same detection logic as main AttributionWidget

### 3. Modified `DetailAttributionWidget` class
- Added early return with `SizedBox.shrink()` for AI-generated images
- Same detection logic as main AttributionWidget

## Detection Logic
AI-generated images are identified by checking the user properties:
```dart
bool get _isAiGenerated {
  return image.user?.name == 'AI Generated' ||
         image.user?.username == 'ai_generated';
}
```

This matches the logic used in `home_cubit.dart` where AI-generated images are created with:
```dart
'user': {
  'name': 'AI Generated',
  'username': 'ai_generated',
  // ...
}
```

## Result
- ✅ Unsplash attribution is hidden for AI-generated images
- ✅ Unsplash attribution still shows for real Unsplash photos
- ✅ No breaking changes to existing functionality
- ✅ Maintains compliance with Unsplash guidelines for actual Unsplash photos

## Files Modified
- `lib/shared/widgets/attribution_widget.dart`

## Testing
The changes can be tested by:
1. Generating an AI image using the prompt enhancement feature
2. Viewing the generated image in both grid and detail views
3. Confirming no Unsplash attribution appears
4. Viewing a regular Unsplash photo to confirm attribution still appears

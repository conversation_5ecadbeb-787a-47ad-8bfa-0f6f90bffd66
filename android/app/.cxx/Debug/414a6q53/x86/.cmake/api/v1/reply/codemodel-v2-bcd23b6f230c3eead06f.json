{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-f5ebdc15457944623624.json", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": "."}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "Project"}], "targets": []}], "kind": "codemodel", "paths": {"build": "/Volumes/SSD/loom_dynamics/my sample/Pixs/android/app/.cxx/Debug/414a6q53/x86", "source": "/Volumes/SSD/fvm/versions/3.29.2/packages/flutter_tools/gradle/src/main/groovy"}, "version": {"major": 2, "minor": 3}}